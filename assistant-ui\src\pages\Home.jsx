import React, { useState, useRef, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Link } from 'react-router-dom'; // For settings icon link

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCog, faMicrophone, faPaperPlane,
  faRobot, faThLarge, faHistory, faPaperclip, faBell,
  faChevronLeft, faChevronRight, faList, faComments,
  faChevronUp, faChevronDown
} from '@fortawesome/free-solid-svg-icons';

import { Button } from "../components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "../components/ui/card";
import { Input } from "../components/ui/input";

import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "../components/ui/resizable";
import TabSystem from "../components/TabSystem";
import StatusBar from '../components/StatusBar';
import FloatingChat from '../components/FloatingChat';

const Home = () => {
  // State for panel visibility - can be adapted for resizable panels later
  const [isDocketVisible, setIsDocketVisible] = useState(false); // Closed by default
  const [isChatHistoryVisible, setIsChatHistoryVisible] = useState(false); // Closed by default
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [availableModels, setAvailableModels] = useState([]);
  const [selectedModel, setSelectedModel] = useState('');
  const messagesEndRef = useRef(null);

  // State for floating input area
  const [isInputVisible, setIsInputVisible] = useState(true);

  // State for auto-hiding header
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [headerHideTimeout, setHeaderHideTimeout] = useState(null);

  // State for dual chat system
  const [currentTabContext, setCurrentTabContext] = useState('chat'); // 'chat' or 'other'

  // System metrics are now handled by StatusBar component

  // Auto-hide header after 3 seconds of inactivity
  useEffect(() => {
    const hideHeader = () => {
      setIsHeaderVisible(false);
    };

    const resetHideTimer = () => {
      if (headerHideTimeout) {
        clearTimeout(headerHideTimeout);
      }
      setIsHeaderVisible(true);
      const timeout = setTimeout(hideHeader, 3000);
      setHeaderHideTimeout(timeout);
    };

    // Initial timer
    resetHideTimer();

    // Mouse movement listener to show header
    const handleMouseMove = (e) => {
      if (e.clientY < 100) { // Show header when mouse is near top
        resetHideTimer();
      }
    };

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      if (headerHideTimeout) {
        clearTimeout(headerHideTimeout);
      }
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [headerHideTimeout]);

  // Load user preferences and available models
  useEffect(() => {
    loadUserPreferences();
    fetchAvailableModels();
  }, []);

  const loadUserPreferences = async () => {
    try {
      const prefs = await invoke('load_user_settings');
      setSelectedModel(prefs.default_ollama_model || '');
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const fetchAvailableModels = async () => {
    try {
      const models = await invoke('get_ollama_models');
      setAvailableModels(models);
      // If no model is selected but models are available, use the first one
      if (!selectedModel && models.length > 0) {
        setSelectedModel(models[0]);
      }
    } catch (error) {
      console.error('Error fetching available models:', error);
      setAvailableModels([]);
    }
  };

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);




  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage = { text: input, sender: 'user', timestamp: new Date() };
    setMessages([...messages, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
        // Check if we have a model selected
        if (!selectedModel) {
          throw new Error('No AI model selected. Please configure a model in settings first.');
        }

        // Send message to AI backend
        const response = await invoke('send_chat_message', {
          model_name: selectedModel,
          prompt: input
        });

        const assistantMessage = {
          text: response,
          sender: 'assistant',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, assistantMessage]);
      } catch (error) {
        console.error('Error querying assistant:', error);
        const errorMessage = {
          text: 'Sorry, I encountered an error processing your request.',
          isError: true
        };
        setMessages(prev => [...prev, errorMessage]);
      } finally {
        setIsLoading(false);
      }
  };
  
  // Handle file drop
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // Handle file upload (simplified, keep existing logic)
  const handleFiles = async (files) => {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const formData = new FormData();
      formData.append('file', file);
      const uploadMessage = { text: `Uploading file: ${file.name}...`, sender: 'system', timestamp: new Date() };
      setMessages(prev => [...prev, uploadMessage]);
      try {
        // TODO: Implement actual file upload logic without axios.
        const successMessage = { text: `File ${file.name} uploaded successfully! (Mock)`, sender: 'system', timestamp: new Date() };
        setMessages(prev => [...prev, successMessage]);
      } catch (error) {
        console.error('Error uploading file:', error);
        const errorMessage = { text: `Error uploading file ${file.name}. (Mock Error)`, sender: 'system', timestamp: new Date(), isError: true };
        setMessages(prev => [...prev, errorMessage]);
      }
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="flex flex-col h-screen bg-background text-foreground" onDrop={handleDrop} onDragOver={handleDragOver}>
      {/* Compact Header - Only visible on chat tab */}
      {currentTabContext === 'chat' && (
        <header className="flex items-center justify-center px-2 py-1 bg-slate-100 dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 h-6">
          <div className="flex items-center space-x-1">
            <div className="bg-gradient-to-r from-blue-600 to-purple-700 p-1 rounded-sm">
              <FontAwesomeIcon icon={faRobot} className="h-3 w-3 text-white" />
            </div>
            <div>
              <h1 className="font-medium text-xs bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                The Collective
              </h1>
            </div>
          </div>
        </header>
      )}

      {/* Tab System */}
      <div className="flex-1 flex flex-col min-h-0"> {/* Removed bottom padding that was causing issues */}
        <TabSystem onTabChange={(tabComponent) => setCurrentTabContext(tabComponent === 'chat' ? 'chat' : 'other')}>
        {/* Main Content Area - Using Resizable Panels */}
        <ResizablePanelGroup direction="horizontal" className="flex-1 min-h-0">
        {/* Enhanced Docket Panel (Left) */}
        <ResizablePanel defaultSize={25} minSize={15} maxSize={40} className={`flex flex-col ${!isDocketVisible ? 'hidden' : ''}`}>
          <Card className="h-full flex flex-col m-3 shadow-xl bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-900/50">
              <CardTitle className="text-lg font-bold text-slate-800 dark:text-slate-200 flex items-center">
                <FontAwesomeIcon icon={faList} className="mr-2 h-5 w-5 text-blue-500" />
                Docket
              </CardTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsDocketVisible(false)}
                className="h-8 w-8 hover:bg-slate-200 dark:hover:bg-slate-700 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
              >
                <FontAwesomeIcon icon={faChevronLeft} className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="p-0 flex-1 overflow-y-auto flex flex-row">
              {/* In Section */}
              <div className="flex-1 p-4 space-y-3 border-r border-slate-200 dark:border-slate-700">
                <h4 className="text-sm font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider mb-3">Incoming</h4>
                {/* Enhanced Docket Item - In */}
                <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 border-blue-200 dark:border-slate-600 hover:shadow-md transition-shadow">
                  <CardContent className="p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="font-semibold text-slate-800 dark:text-slate-200">file.mp4</div>
                      <div className="text-xs text-slate-500 dark:text-slate-400">12.7 MB</div>
                    </div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">Yesterday, 10:30 AM</div>
                    <div className="flex items-center space-x-2">
                      <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">Indexing...</div>
                      <div className="flex-1 bg-slate-200 dark:bg-slate-600 rounded-full h-1.5">
                        <div className="bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 rounded-full" style={{width: '75%'}}></div>
                      </div>
                      <div className="text-xs text-slate-500 dark:text-slate-400">75%</div>
                    </div>
                  </CardContent>
                </Card>
                {/* Placeholder for more 'In' items */}
                <div className="text-xs text-slate-500 dark:text-slate-400 p-3 text-center italic">
                  More incoming items will appear here
                </div>
              </div>

              {/* Out Section */}
              <div className="flex-1 p-4 space-y-3">
                <h4 className="text-sm font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider mb-3">Outgoing</h4>
                {/* Placeholder for 'Out' items */}
                <div className="text-xs text-slate-500 dark:text-slate-400 p-3 text-center italic">
                  Outgoing items will appear here
                </div>
              </div>
            </CardContent>
          </Card>
        </ResizablePanel>
        {!isDocketVisible && (
          <Button variant="outline" size="icon" onClick={() => setIsDocketVisible(true)} className="absolute top-1/2 left-0 -translate-y-1/2 z-10 h-10 w-10 rounded-r-md rounded-l-none border-l-0">
            <FontAwesomeIcon icon={faList} className="h-5 w-5" />
          </Button>
        )}

        <ResizableHandle withHandle className={`${!isDocketVisible || !isChatHistoryVisible ? 'hidden' : ''}`} />

        {/* Enhanced Center Chat Area */}
        <ResizablePanel defaultSize={50} minSize={30} className="flex flex-col bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-800">
          <section className="flex-1 flex flex-col p-4 space-y-3 min-h-0">
            <div className="flex-1 overflow-y-auto space-y-4 pb-4">
              {messages.length === 0 && !isLoading ? (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <div className="relative mb-6">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full blur-xl opacity-30"></div>
                    <div className="relative bg-gradient-to-r from-blue-600 to-purple-700 p-6 rounded-full">
                      <FontAwesomeIcon icon={faRobot} className="h-12 w-12 text-white" />
                    </div>
                  </div>
                  <h2 className="text-3xl font-bold mb-3 bg-gradient-to-r from-slate-800 to-slate-600 dark:from-slate-200 dark:to-slate-400 bg-clip-text text-transparent">
                    How can I help you today?
                  </h2>
                  <p className="text-slate-600 dark:text-slate-400 text-lg max-w-md">
                    Ask me anything or explore the tools in the sidebars to get started.
                  </p>
                  <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
                    <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50">
                      <div className="text-blue-600 dark:text-blue-400 mb-2">
                        <FontAwesomeIcon icon={faComments} className="h-5 w-5" />
                      </div>
                      <h3 className="font-semibold text-slate-800 dark:text-slate-200 mb-1">Start a Conversation</h3>
                      <p className="text-sm text-slate-600 dark:text-slate-400">Ask questions, get help, or just chat</p>
                    </Card>
                    <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50">
                      <div className="text-purple-600 dark:text-purple-400 mb-2">
                        <FontAwesomeIcon icon={faThLarge} className="h-5 w-5" />
                      </div>
                      <h3 className="font-semibold text-slate-800 dark:text-slate-200 mb-1">Explore Tools</h3>
                      <p className="text-sm text-slate-600 dark:text-slate-400">Browse available applications and features</p>
                    </Card>
                  </div>
                </div>
              ) : (
                messages.map((message, index) => (
                  <div key={index} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs lg:max-w-md px-5 py-3 rounded-2xl shadow-lg ${
                      message.sender === 'user'
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                        : 'bg-white dark:bg-slate-800 text-slate-800 dark:text-slate-200 border border-slate-200 dark:border-slate-700'
                    } ${message.isError ? 'bg-gradient-to-r from-red-500 to-red-600 text-white' : ''}`}>
                      <p className="text-sm leading-relaxed">{message.text}</p>
                      <p className={`text-xs mt-2 ${
                        message.sender === 'user'
                          ? 'text-blue-100'
                          : 'text-slate-500 dark:text-slate-400'
                      }`}>
                        {formatTime(message.timestamp)}
                      </p>
                    </div>
                  </div>
                ))
              )}
              {isLoading && (
                <div className="flex justify-start">
                    <div className="max-w-xs lg:max-w-md px-5 py-3 rounded-2xl shadow-lg bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
                        <div className="flex items-center space-x-2">
                            <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                            <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                            <div className="h-2 w-2 bg-blue-500 rounded-full animate-bounce"></div>
                            <span className="text-sm text-slate-600 dark:text-slate-400 ml-2">Thinking...</span>
                        </div>
                    </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </section>
        </ResizablePanel>

        <ResizableHandle withHandle className={`${!isDocketVisible || !isChatHistoryVisible ? 'hidden' : ''}`} />

        {/* Enhanced Chat History Panel (Right) */}
        <ResizablePanel defaultSize={25} minSize={15} maxSize={40} className={`flex flex-col ${!isChatHistoryVisible ? 'hidden' : ''}`}>
          <Card className="h-full flex flex-col m-3 shadow-xl bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-900/50">
              <CardTitle className="text-lg font-bold text-slate-800 dark:text-slate-200 flex items-center">
                <FontAwesomeIcon icon={faHistory} className="mr-2 h-5 w-5 text-purple-500" />
                Chat History
              </CardTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsChatHistoryVisible(false)}
                className="h-8 w-8 hover:bg-slate-200 dark:hover:bg-slate-700 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
              >
                <FontAwesomeIcon icon={faChevronRight} className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="p-4 flex-1 overflow-y-auto space-y-3">
              {/* Enhanced chat history items */}
              <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-slate-800 dark:to-slate-700 border-purple-200 dark:border-slate-600 hover:shadow-md transition-all cursor-pointer group">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <p className="font-semibold text-slate-800 dark:text-slate-200 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                      Case Briefing - Project Alpha
                    </p>
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                  </div>
                  <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">Yesterday, 3:45 PM</p>
                  <p className="text-xs text-slate-500 dark:text-slate-500 line-clamp-2">
                    Discussed legal precedents and case analysis for the ongoing project...
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 border-blue-200 dark:border-slate-600 hover:shadow-md transition-all cursor-pointer group">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <p className="font-semibold text-slate-800 dark:text-slate-200 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      Legal Precedents for X
                    </p>
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  </div>
                  <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">2 days ago, 10:12 AM</p>
                  <p className="text-xs text-slate-500 dark:text-slate-500 line-clamp-2">
                    Research session on relevant legal precedents and their applications...
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-slate-800 dark:to-slate-700 border-green-200 dark:border-slate-600 hover:shadow-md transition-all cursor-pointer group">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <p className="font-semibold text-slate-800 dark:text-slate-200 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                      Document Analysis
                    </p>
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  </div>
                  <p className="text-xs text-slate-600 dark:text-slate-400 mb-2">3 days ago, 2:20 PM</p>
                  <p className="text-xs text-slate-500 dark:text-slate-500 line-clamp-2">
                    Comprehensive analysis of contract documents and terms...
                  </p>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </ResizablePanel>
        {!isChatHistoryVisible && (
          <Button variant="outline" size="icon" onClick={() => setIsChatHistoryVisible(true)} className="absolute top-1/2 right-0 -translate-y-1/2 z-10 h-10 w-10 rounded-l-md rounded-r-none border-r-0">
            <FontAwesomeIcon icon={faHistory} className="h-5 w-5" />
          </Button>
        )}
        </ResizablePanelGroup>

        {/* Chat Input Section - Fixed at bottom with proper spacing */}
        {currentTabContext === 'chat' && (
          <div className="p-3 border-t border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 shadow-lg">
            <form className="flex items-center space-x-2" onSubmit={handleSubmit}>
              <div className="flex-1 relative">
                <Input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Message The Collective..."
                  disabled={isLoading}
                  className="w-full pl-3 pr-10 py-2 bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                />
                {input.trim() && (
                  <div className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-slate-400">
                    {input.length}
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                type="button"
                onClick={() => alert('Microphone clicked!')}
                disabled={isLoading}
                className="h-8 w-8 hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors"
              >
                <FontAwesomeIcon icon={faMicrophone} className="h-4 w-4" />
              </Button>
              <input type="file" id="file-input-home" multiple onChange={(e) => handleFiles(e.target.files)} className="hidden" />
              <Button
                variant="ghost"
                size="icon"
                type="button"
                onClick={() => document.getElementById('file-input-home').click()}
                disabled={isLoading}
                className="h-8 w-8 hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors"
              >
                <FontAwesomeIcon icon={faPaperclip} className="h-4 w-4" />
              </Button>
              <Button
                type="submit"
                size="icon"
                disabled={isLoading || !input.trim()}
                className="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FontAwesomeIcon icon={faPaperPlane} className="h-3 w-3" />
              </Button>
            </form>
          </div>
        )}
        </TabSystem>
      </div>

      {/* Floating Chat System for Non-Chat Pages */}
      {currentTabContext !== 'chat' && (
        <FloatingChat onSubmit={(message) => handleSubmit({ preventDefault: () => {}, target: { elements: { message: { value: message } } } })} isLoading={isLoading} />
      )}

      {/* VS Code Style Status Bar - Always at bottom */}
      <StatusBar />
    </div>
  );
};

export default Home;