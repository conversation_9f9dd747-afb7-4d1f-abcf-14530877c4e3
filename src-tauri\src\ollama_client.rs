use serde::{Deserialize, Serialize};
use std::process::{Child, Command};
use std::sync::{Arc, Mutex};
use tokio::time::{sleep, Duration};
use reqwest::Client;
use tauri::Manager;

#[derive(Debug)]
pub enum OllamaError {
    Reqwest(reqwest::Error),
    Io(std::io::Error),
    <PERSON><PERSON>(serde_json::Error),
    Custom(String),
    RequestFailed(String),
    ResponseParseError(String),
}

impl From<reqwest::Error> for OllamaError {
    fn from(err: reqwest::Error) -> Self {
        OllamaError::Reqwest(err)
    }
}

impl From<std::io::Error> for OllamaError {
    fn from(err: std::io::Error) -> Self {
        OllamaError::Io(err)
    }
}

impl From<serde_json::Error> for OllamaError {
    fn from(err: serde_json::Error) -> Self {
        OllamaError::J<PERSON>(err)
    }
}

impl std::fmt::Display for OllamaError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            OllamaError::Reqwest(err) => write!(f, "Reqwest error: {}", err),
            OllamaError::Io(err) => write!(f, "IO error: {}", err),
            OllamaError::Json(err) => write!(f, "JSON error: {}", err),
            OllamaError::Custom(err) => write!(f, "Custom error: {}", err),
            OllamaError::RequestFailed(err) => write!(f, "Request failed: {}", err),
            OllamaError::ResponseParseError(err) => write!(f, "Response parse error: {}", err),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OllamaStatus {
    pub status: String,
}

pub struct OllamaClient {
    client: Client,
    ollama_process: Arc<Mutex<Option<Child>>>, // Changed to Arc<Mutex<Option<Child>>>
    base_url: String,
}

impl OllamaClient {
    pub fn new(base_url: String) -> Self {
        OllamaClient {
            client: Client::new(),
            ollama_process: Arc::new(Mutex::new(None)),
            base_url,
        }
    }



    pub async fn start_ollama(&self, server_path: Option<&str>) -> Result<(), OllamaError> {
        // Check if already running first (without holding the lock)
        {
            let process_guard = self.ollama_process.lock().unwrap();
            if process_guard.is_some() {
                return Err(OllamaError::Custom("Server process already running".to_string()));
            }
        } // Drop the guard here

        println!("Attempting to start server from profile...");

        let server_executable = if let Some(server_path) = server_path {
            // Use enhanced server detection from the new profile system
            let server_dir = std::path::Path::new(server_path);
            println!("Looking for server executable in: {}", server_dir.display());

            // Use the centralized detection system from settings_manager
            let detected_executables = crate::settings_manager::detect_server_executables(server_dir);

            if let Some(primary_executable) = detected_executables.first() {
                println!("Found server executable: {} (type: {}, priority: {})",
                    primary_executable.path,
                    primary_executable.executable_type,
                    primary_executable.priority
                );

                // Log all detected executables for debugging
                if detected_executables.len() > 1 {
                    println!("Additional executables found:");
                    for (i, exec) in detected_executables.iter().skip(1).enumerate() {
                        println!("  {}. {} (type: {}, priority: {})",
                            i + 2, exec.file_name, exec.executable_type, exec.priority);
                    }
                }

                primary_executable.path.clone()
            } else {
                // Enhanced error message with directory contents
                println!("No executables found. Directory contents:");
                if let Ok(entries) = std::fs::read_dir(server_dir) {
                    for entry in entries {
                        if let Ok(entry) = entry {
                            let path = entry.path();
                            let file_name = path.file_name()
                                .and_then(|n| n.to_str())
                                .unwrap_or("unknown");
                            println!("  - {} ({})", file_name, if path.is_dir() { "directory" } else { "file" });
                        }
                    }
                }

                return Err(OllamaError::Custom(format!(
                    "No executable found in server profile directory: {}.\n\nSupported executable types:\n- Windows: .exe, .bat, .cmd, .ps1\n- Unix/Linux: executable files, .sh, .bash\n- Cross-platform: .py, .jar, .js, .rb, .pl, .php\n\nPlease ensure a server executable is in this folder.",
                    server_dir.display()
                )));
            }
        } else {
            // No server path provided and no profile found - cannot start
            return Err(OllamaError::Custom("No server profile configured. Please configure a server profile in settings.".to_string()));
        };

        println!("Starting server from: {}", server_executable);

        // Set up environment for portable operation
        let mut cmd = Command::new(&server_executable);
        cmd.arg("serve");

        // Redirect stdout and stderr for debugging
        cmd.stdout(std::process::Stdio::piped());
        cmd.stderr(std::process::Stdio::piped());

        // Set environment variables for portable operation
        // Use the configured models path from user preferences
        if let Ok(models_path) = crate::settings_manager::get_models_path().await {
            if !models_path.is_empty() {
                println!("Set models path environment to: {}", models_path);
                // Set common model path environment variables
                cmd.env("OLLAMA_MODELS", &models_path);
                cmd.env("MODELS_PATH", &models_path);
                cmd.env("MODEL_DIR", &models_path);
            }
        }

        // Now spawn the process and store it
        {
            let mut process_guard = self.ollama_process.lock().unwrap();

            match cmd.spawn() {
                Ok(child) => {
                    println!("Server process spawned successfully with PID: {:?}", child.id());
                    *process_guard = Some(child);
                    println!("Server process started successfully.");
                },
                Err(e) => {
                    println!("Failed to spawn server process: {}", e);
                    println!("Attempted to start: {}", server_executable);
                    println!("Working directory: {:?}", std::env::current_dir());

                    // Check if the executable exists and is accessible
                    let exe_path = std::path::Path::new(&server_executable);
                    if exe_path.exists() {
                        println!("Executable exists but failed to start. Error: {}", e);
                        if exe_path.is_file() {
                            println!("File is accessible");
                        } else {
                            println!("Path exists but is not a file");
                        }
                    } else {
                        println!("Executable does not exist at path: {}", server_executable);
                    }

                    return Err(OllamaError::Custom(format!(
                        "Failed to start server from {}: {}. Check if the executable exists and is valid.",
                        server_executable, e
                    )));
                }
            }
        } // Drop the guard here

        // Wait for server to be ready
        for _ in 0..30 { // Try for 30 seconds
            if self.is_server_running().await {
                println!("Server is running and ready.");
                return Ok(());
            }
            sleep(Duration::from_secs(1)).await;
        }
        Err(OllamaError::Custom("Server did not start in time".to_string()))
    }



    pub async fn stop_server(&self) -> Result<(), OllamaError> {
        let mut process_guard = self.ollama_process.lock().unwrap();
        if let Some(mut child) = process_guard.take() {
            child.kill()?;
            child.wait()?;
            println!("Server process stopped.");
        }
        Ok(())
    }



    pub async fn get_server_status_internal(&self) -> Result<OllamaStatus, OllamaError> {
        // First check if we have an active server profile
        use crate::settings_manager::UserPreferences;
        let prefs = UserPreferences::load();
        let has_active_server = prefs.server_profiles.iter().any(|(_, profile)| profile.enabled);

        if !has_active_server {
            return Ok(OllamaStatus { status: "no_server_profile".to_string() });
        }

        // Check if we have a process running
        let has_process = {
            let mut process_guard = self.ollama_process.lock().unwrap();
            if let Some(ref mut child) = *process_guard {
                // Check if the process is still alive
                match child.try_wait() {
                    Ok(Some(_)) => {
                        // Process has exited, clean it up
                        *process_guard = None;
                        false
                    },
                    Ok(None) => true,     // Process is still running
                    Err(_) => {
                        // Error checking process, assume it's dead
                        *process_guard = None;
                        false
                    }
                }
            } else {
                false
            }
        }; // Guard is dropped here

        if has_process {
            // We have a process, check if it's responding
            if self.is_server_running().await {
                Ok(OllamaStatus { status: "running".to_string() })
            } else {
                // Process exists but not responding - might be starting
                Ok(OllamaStatus { status: "starting".to_string() })
            }
        } else {
            // No process running, but check if server is running externally
            if self.is_server_running().await {
                Ok(OllamaStatus { status: "running_external".to_string() })
            } else {
                Ok(OllamaStatus { status: "stopped".to_string() })
            }
        }
    }

    pub async fn is_server_running(&self) -> bool {
        let url = format!("{}/api/tags", self.base_url);
        match self.client.get(&url).send().await {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }



    pub async fn get_ollama_models_internal(&self) -> Result<Vec<String>, OllamaError> {
        let url = format!("{}/api/tags", self.base_url);
        let response = self.client.get(&url).send().await?.json::<serde_json::Value>().await?;
        let models: Vec<String> = response["models"].as_array()
            .unwrap_or(&vec![])
            .iter()
            .filter_map(|m| m["name"].as_str().map(|s| s.to_string()))
            .collect();
        Ok(models)
    }



    pub async fn pull_ollama_model_internal(&self, model_name: &str) -> Result<(), OllamaError> {
        // First check if server is running
        if !self.is_server_running().await {
            return Err(OllamaError::Custom("Server is not running. Please start the server first.".to_string()));
        }

        let url = format!("{}/api/pull", self.base_url);
        let body = serde_json::json!({ "name": model_name });

        println!("Pulling model: {}", model_name);

        let response = self.client.post(&url).json(&body).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(OllamaError::Custom(format!("Failed to pull model {}: {}", model_name, error_text)));
        }

        println!("Successfully initiated pull for model: {}", model_name);
        Ok(())
    }

    // Chat completion method for your dashboard
    pub async fn generate_completion(&self, model_name: &str, prompt: &str) -> Result<String, OllamaError> {
        let url = format!("{}/api/generate", self.base_url);
        let body = serde_json::json!({
            "model": model_name,
            "prompt": prompt,
            "stream": false
        });

        let response = self.client.post(&url).json(&body).send().await?;
        let json: serde_json::Value = response.json().await?;

        if let Some(response_text) = json["response"].as_str() {
            Ok(response_text.to_string())
        } else {
            Err(OllamaError::Custom("No response from model".to_string()))
        }
    }

    // Chat method for conversation-style interactions
    pub async fn chat(&self, model_name: &str, messages: Vec<serde_json::Value>) -> Result<String, OllamaError> {
        let url = format!("{}/api/chat", self.base_url);
        let body = serde_json::json!({
            "model": model_name,
            "messages": messages,
            "stream": false
        });

        let response = self.client.post(&url).json(&body).send().await?;
        let json: serde_json::Value = response.json().await?;

        if let Some(message) = json["message"]["content"].as_str() {
            Ok(message.to_string())
        } else {
            Err(OllamaError::Custom("No response from chat".to_string()))
        }
    }
}

impl Drop for OllamaClient {
    fn drop(&mut self) {
        if let Ok(mut process_guard) = self.ollama_process.lock() {
            if let Some(mut child) = process_guard.take() {
                let _ = child.kill();
                let _ = child.wait();
            }
        }
    }
}

// Tauri commands
#[tauri::command]
pub async fn start_ollama_server(app_handle: tauri::AppHandle) -> Result<(), String> {
    use crate::settings_manager::{get_servers_path, UserPreferences};

    // Get the active server profile from user preferences
    let prefs = UserPreferences::load();
    let servers_path = get_servers_path().await.unwrap_or_default();

    println!("=== DEBUG: Starting Server ===");
    println!("Servers path: {}", servers_path);
    println!("Server profiles: {:?}", prefs.server_profiles);

    // Find the first enabled server profile
    let active_server = prefs.server_profiles.iter()
        .find(|(_, profile)| profile.enabled)
        .map(|(name, _)| name.clone());

    println!("Active server found: {:?}", active_server);

    let server_path_option = if let Some(active_server_name) = active_server {
        // Get the server profile and use its folder_path directly
        if let Some(profile) = prefs.server_profiles.get(&active_server_name) {
            println!("Starting server from active profile: {} at path: {}", active_server_name, profile.folder_path);
            Some(profile.folder_path.clone())
        } else {
            println!("Active server profile '{}' not found in preferences", active_server_name);
            None
        }
    } else {
        // If no active server is set, try to find the first enabled server profile
        println!("No active server profile set, searching for enabled profiles...");

        // Get server profiles to find an enabled one
        if let Ok(profiles) = crate::settings_manager::get_server_profiles().await {
            let enabled_profile = profiles.iter().find(|p| p.enabled);

            if let Some(profile) = enabled_profile {
                println!("Found enabled server profile: {}", profile.name);
                Some(profile.folder_path.clone())
            } else {
                println!("No enabled server profiles found, cannot start server");
                None
            }
        } else {
            println!("Failed to get server profiles, cannot start server");
            None
        }
    };

    println!("Final server path option: {:?}", server_path_option);

    let client = app_handle.state::<OllamaClient>();

    match client.start_ollama(server_path_option.as_deref()).await {
        Ok(_) => {
            println!("Server started successfully from: {}",
                server_path_option.unwrap_or_else(|| "system PATH".to_string()));
            Ok(())
        },
        Err(e) => {
            println!("Failed to start server: {}", e);
            Err(e.to_string())
        },
    }
}

#[tauri::command]
pub async fn stop_ollama_server(app_handle: tauri::AppHandle) -> Result<(), String> {
    let client = app_handle.state::<OllamaClient>();
    match client.stop_server().await {
        Ok(_) => Ok(()),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_ollama_status(app_handle: tauri::AppHandle) -> Result<OllamaStatus, String> {
    let client = app_handle.state::<OllamaClient>();
    match client.get_server_status_internal().await {
        Ok(status) => Ok(status),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_ollama_models(app_handle: tauri::AppHandle) -> Result<Vec<String>, String> {
    let client = app_handle.state::<OllamaClient>();
    match client.get_ollama_models_internal().await {
        Ok(models) => Ok(models),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn pull_ollama_model(app_handle: tauri::AppHandle, model_name: String) -> Result<(), String> {
    let client = app_handle.state::<OllamaClient>();
    match client.pull_ollama_model_internal(&model_name).await {
        Ok(_) => Ok(()),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn send_chat_message(
    app_handle: tauri::AppHandle,
    model_name: String,
    prompt: String
) -> Result<String, String> {
    let client = app_handle.state::<OllamaClient>();
    match client.generate_completion(&model_name, &prompt).await {
        Ok(response) => Ok(response),
        Err(e) => Err(e.to_string()),
    }
}

// Helper function to check if Ollama is installed in the portable location
#[tauri::command]
pub async fn check_ollama_installation(_app_handle: tauri::AppHandle) -> Result<String, String> {
    use crate::settings_manager::{get_servers_path, get_models_path, UserPreferences};

    let prefs = UserPreferences::load();
    let servers_path = get_servers_path().await.unwrap_or_default();
    let models_path = get_models_path().await.unwrap_or_default();

    if servers_path.is_empty() {
        return Err("Server path not configured. Please set the server path in settings.".to_string());
    }

    // Find the first enabled server profile
    let active_server = prefs.server_profiles.iter()
        .find(|(_, profile)| profile.enabled)
        .map(|(name, _)| name.clone());

    if let Some(active_server_name) = active_server {
        let server_dir = std::path::Path::new(&servers_path).join(&active_server_name);

        // Use the enhanced detection system
        let detected_executables = crate::settings_manager::detect_server_executables(&server_dir);

        if let Some(primary_executable) = detected_executables.first() {
            let exe_path = std::path::Path::new(&primary_executable.path);
            let mut status = format!("✓ Server executable found at: {}\n", exe_path.display());
            status.push_str(&format!("✓ Executable type: {}\n", primary_executable.executable_type));
            status.push_str(&format!("✓ Active server profile: {}\n", active_server_name));
            status.push_str(&format!("✓ Server path: {}\n", servers_path));
            status.push_str(&format!("✓ Models path: {}\n", models_path));

            // Show additional executables if found
            if detected_executables.len() > 1 {
                status.push_str(&format!("ℹ Additional executables found ({}):\n", detected_executables.len() - 1));
                for exec in detected_executables.iter().skip(1) {
                    status.push_str(&format!("  - {} ({})\n", exec.file_name, exec.executable_type));
                }
            }

            // Check if models directory exists
            if std::path::Path::new(&models_path).exists() {
                status.push_str("✓ Models directory exists\n");
            } else {
                status.push_str("⚠ Models directory does not exist (will be created)\n");
            }

            Ok(status)
        } else {
            Err(format!(
                "✗ No executable found in: {}\n\nSupported executable types:\n- Windows: .exe, .bat, .cmd, .ps1\n- Unix/Linux: executable files, .sh, .bash\n- Cross-platform: .py, .jar, .js, .rb, .pl, .php\n\nActive server profile: {}\nCurrent server path: {}\nCurrent models path: {}",
                server_dir.display(),
                active_server_name,
                servers_path,
                models_path
            ))
        }
    } else {
        Err("No active server profile found. Please enable a server profile first.".to_string())
    }
}