{"$message_type": "diagnostic", "message": "unused import: `std::path::Path`", "code": {"code": "unused_imports", "explanation": null}, "level": "warning", "spans": [{"file_name": "src\\plugin_manager.rs", "byte_start": 54, "byte_end": 69, "line_start": 3, "line_end": 3, "column_start": 5, "column_end": 20, "is_primary": true, "text": [{"text": "use std::path::Path;", "highlight_start": 5, "highlight_end": 20}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "`#[warn(unused_imports)]` on by default", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "remove the whole `use` item", "code": null, "level": "help", "spans": [{"file_name": "src\\plugin_manager.rs", "byte_start": 50, "byte_end": 71, "line_start": 3, "line_end": 4, "column_start": 1, "column_end": 1, "is_primary": true, "text": [{"text": "use std::path::Path;", "highlight_start": 1, "highlight_end": 21}, {"text": "use std::env;", "highlight_start": 1, "highlight_end": 1}], "label": null, "suggested_replacement": "", "suggestion_applicability": "MachineApplicable", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::path::Path`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\plugin_manager.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::path::Path;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}