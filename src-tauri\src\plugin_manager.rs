use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;
use std::env;
use crate::settings_manager::UserPreferences;

// Helper function to resolve relative paths from app directory
fn resolve_path(path: &str) -> Result<std::path::PathBuf, String> {
    if path.starts_with("./") {
        // Always use current working directory for relative paths
        // This ensures we use E:\TheCollective\Storage not E:\TheCollective\src-tauri\Storage
        let cwd = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
        let resolved = cwd.join(&path[2..]);
        Ok(resolved)
    } else {
        Ok(std::path::PathBuf::from(path))
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Plugin {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    pub enabled: bool,
    pub path: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct PluginJson {
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    pub main: Option<String>,
    pub class_name: Option<String>,
    pub plugin_type: Option<String>,
}

fn default_enabled() -> bool {
    true
}

#[tauri::command]
pub async fn get_plugins() -> Result<Vec<Plugin>, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    // Reduced debug logging

    if !plugins_dir.exists() {
        return Err(format!("Plugins directory does not exist: {}", plugins_dir.display()));
    }

    // Reduced debug logging

    let mut plugins = Vec::new();
    let mut plugin_id = 1;

    // Scan directory directly for plugins
    for entry in fs::read_dir(&plugins_dir).map_err(|e| format!("Failed to read plugins directory: {}", e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let plugin_path = entry.path();

        // Reduced debug logging

        if plugin_path.is_dir() {
            let plugin_json_path = plugin_path.join("plugin.json");
            if plugin_json_path.exists() {
                match fs::read_to_string(&plugin_json_path) {
                    Ok(contents) => {
                        match serde_json::from_str::<PluginJson>(&contents) {
                            Ok(plugin_data) => {
                                plugins.push(Plugin {
                                    id: plugin_id,
                                    name: plugin_data.name.clone(),
                                    description: plugin_data.description.or_else(|| Some("No description".to_string())),
                                    version: plugin_data.version,
                                    enabled: plugin_data.enabled,
                                    path: plugin_path.to_string_lossy().to_string(),
                                });
                                plugin_id += 1;
                            }
                            Err(e) => {
                                eprintln!("Failed to parse plugin.json in {}: {}", plugin_path.display(), e);
                            }
                        }
                    }
                    Err(e) => {
                        eprintln!("Failed to read plugin.json in {}: {}", plugin_path.display(), e);
                    }
                }
            }
        }
    }

    println!("DEBUG: Total plugins found: {}", plugins.len());
    Ok(plugins)
}

#[tauri::command]
pub async fn toggle_plugin(plugin_name: String, enabled: bool) -> Result<String, String> {
    let prefs = UserPreferences::load();
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    if !plugins_dir.exists() {
        return Err(format!("Plugins directory does not exist: {}", plugins_dir.display()));
    }

    // Find the plugin directory
    for entry in fs::read_dir(&plugins_dir).map_err(|e| format!("Failed to read plugins directory: {}", e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let plugin_path = entry.path();

        if plugin_path.is_dir() {
            let plugin_json_path = plugin_path.join("plugin.json");
            if plugin_json_path.exists() {
                let contents = fs::read_to_string(&plugin_json_path)
                    .map_err(|e| format!("Failed to read plugin.json: {}", e))?;
                let mut plugin_data: PluginJson = serde_json::from_str(&contents)
                    .map_err(|e| format!("Failed to parse plugin.json: {}", e))?;

                if plugin_data.name == plugin_name {
                    // Toggle the enabled state
                    plugin_data.enabled = enabled;

                    // Write back to file
                    let updated_contents = serde_json::to_string_pretty(&plugin_data)
                        .map_err(|e| format!("Failed to serialize plugin.json: {}", e))?;
                    fs::write(&plugin_json_path, updated_contents)
                        .map_err(|e| format!("Failed to write plugin.json: {}", e))?;

                    return Ok(format!("Plugin {} {}", plugin_name, if enabled { "enabled" } else { "disabled" }));
                }
            }
        }
    }

    Err(format!("Plugin {} not found", plugin_name))
}

#[tauri::command]
pub async fn refresh_plugins() -> Result<Vec<Plugin>, String> {
    // Just call get_plugins since it scans the filesystem directly
    get_plugins().await
}

#[tauri::command]
pub async fn debug_plugin_loading() -> Result<String, String> {
    let mut debug_info = String::new();

    // Get plugins path from user preferences
    let prefs = UserPreferences::load();
    debug_info.push_str(&format!("Plugins path (from preferences): {}\n", prefs.plugins_path));

    // Resolve relative paths to absolute paths
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    debug_info.push_str(&format!("Plugins path (resolved): {}\n", plugins_dir.display()));
    debug_info.push_str(&format!("Directory exists: {}\n", plugins_dir.exists()));

    if plugins_dir.exists() {
        match fs::read_dir(&plugins_dir) {
            Ok(entries) => {
                debug_info.push_str("Directory contents:\n");
                for entry in entries {
                    match entry {
                        Ok(entry) => {
                            let path = entry.path();
                            debug_info.push_str(&format!("  - {} ({})\n",
                                path.file_name().unwrap_or_default().to_string_lossy(),
                                if path.is_dir() { "directory" } else { "file" }
                            ));

                            if path.is_dir() {
                                let plugin_json = path.join("plugin.json");
                                debug_info.push_str(&format!("    plugin.json exists: {}\n", plugin_json.exists()));
                                if plugin_json.exists() {
                                    match fs::read_to_string(&plugin_json) {
                                        Ok(content) => {
                                            debug_info.push_str(&format!("    plugin.json content: {}\n", content));
                                        }
                                        Err(e) => {
                                            debug_info.push_str(&format!("    Error reading plugin.json: {}\n", e));
                                        }
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            debug_info.push_str(&format!("  Error reading entry: {}\n", e));
                        }
                    }
                }
            }
            Err(e) => {
                debug_info.push_str(&format!("Error reading directory: {}\n", e));
            }
        }
    }

    Ok(debug_info)
}


